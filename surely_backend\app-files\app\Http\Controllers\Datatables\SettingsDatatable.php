<?php

namespace App\Http\Controllers\Datatables;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Yajra\DataTables\Facades\DataTables;


class SettingsDatatable extends Controller 
{
    public function loadModal($id, $action)
    {
        switch ($action) {
            case 'edit':
                return View::make('settings.modals.update')->with(['user' => User::findOrFail($id)])->render();
            case 'add_language':
                return View::make('settings.modals.addLanguage')->render();
            default:
                return '';
        }
    }
}