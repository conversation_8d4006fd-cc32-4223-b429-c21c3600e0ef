<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const TYPE_ESCROW = 'escrow';
    const TYPE_PAYMENT = 'payment';

    protected $fillable = [
        'contract_id',
        'job_id',
        'client_id',
        'operator_id',
        'amount',
        'currency',
        'description',
        'stripe_id',
        'stripe_customer_id',
        'stripe_payment_intent_id',
        'stripe_payment_intent_client_secret',
        'stripe_payment_intent_status',
        'stripe_payment_invoice_id',
        'type',
        'status',
        'sub_amount',
        'payment_status',
        'application_vat_rate',
        'application_vat_amount',
        'operator_vat_rate',
        'operator_vat_amount',
        'application_fee_rate',
        'application_fee_amount',
    ];

    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
}
