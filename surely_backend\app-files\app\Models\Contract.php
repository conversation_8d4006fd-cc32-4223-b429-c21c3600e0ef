<?php

namespace App\Models;

use DateTime;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Contract extends Model
{
    use HasFactory;

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($contract) {
            // Calculate emergency hire fee if applicable
            if ($contract->job && $contract->job->is_emergency_hire) {
                $contract->emergency_hire_fee_rate = self::EMERGENCY_HIRE_FEE_RATE;
                $contract->emergency_hire_fee_amount = round($contract->sub_total * (self::EMERGENCY_HIRE_FEE_RATE / 100), 2);
            } else {
                $contract->emergency_hire_fee_rate = 0;
                $contract->emergency_hire_fee_amount = 0;
            }

            // Calculate total amount including all fees
            $contract->total_amount = $contract->sub_total + 
                                    $contract->application_fee_amount + 
                                    $contract->operator_vat_amount + 
                                    $contract->application_vat_amount + 
                                    $contract->emergency_hire_fee_amount +
                                    $contract->payment_transaction_fee_amount; // Updated
        });
    }

    protected $fillable = [
        'job_id',
        'client_id',
        'operative_id',
        'chat_id',
        'hourly_rate',
        'location',
        'type',
        'status',
        'start_date',
        'end_date',
        'date_range',
        'shifts_status',
        'payment_terms',
        'escrow_status',
        'escrow_amount',
        'payment_status',
        'total_amount',
        'shifts_accepted_at',
        'reason',
        'application_vat_amount',
        'application_vat_rate',
        'sub_total',
        'application_fee_amount',
        'application_fee_rate',
        'operator_vat_amount',
        'operator_vat_rate',
        'escrow_rate',
        'emergency_hire_fee_rate',
        'emergency_hire_fee_amount',
        'payment_transaction_fee_amount',
        'payment_transaction_fee_rate',
    ];

    protected $casts = [
        'date_range' => 'array',
    ];

    const APPLICATION_VAT_RATE = 20;
    const APPLICATION_FEE_RATE = 20;
    const EMERGENCY_HIRE_FEE_RATE = 5;
    const TRANSACTION_FEE_RATE = 20;
    const invited = 1;
    const pending = 2;
    const in_progress = 3;
    const complete = 4;
    const canceled = 5;
    const rejected = 6;

    //payment types
    const PAYMENT_STATUS_PENDING = 'pending';
    const PAYMENT_STATUS_PAID = 'paid';
    const PAYMENT_STATUS_REFUNDED = 'refunded';
    const PAYMENT_STATUS_FAILED = 'failed';

    //shifts status
    const SHIFT_STATUS_PENDING = 'pending';
    const SHIFT_STATUS_ACCEPTED = 'accepted';
    const SHIFT_STATUS_COMPLETED = 'completed';
    const SHIFT_STATUS_CONFIRMED = 'confirmed';
    const SHIFT_STATUS_NEED_REVIEW = 'need_review';


    //escrow status
    const ESCROW_STATUS_PENDING = 'pending';
    const ESCROW_STATUS_PAID = 'paid';
    const ESCROW_STATUS_REFUNDED = 'refunded';


    public function client(): BelongsTo
    {
        return $this->belongsTo(MobileUser::class, 'client_id', 'id');
    }


    public function operative(): BelongsTo
    {
        return $this->belongsTo(MobileUser::class, 'operative_id', 'id');
    }

    public function chat(): BelongsTo
    {
        return $this->belongsTo(Chat::class, 'chat_id', 'id');
    }

    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class, 'job_id', 'id');
    }

    public function getStartDate()
    {
        if (! $this->date_range) {
            return null;
        }

        $dates = $this->date_range;
        $startDates = array_map(function ($date) {
            return date('Y-m-d', strtotime($date['start']));
        }, $dates);

        return min($startDates) ?? null;
    }

    public function getEndDate()
    {
        if (! $this->date_range) {
            return null;
        }

        $dates = $this->date_range ?? [];
        $endDates = array_map(function ($date) {
            return date('Y-m-d', strtotime($date['end']));
        }, $dates);

        return max($endDates) ?? null;
    }

    public function shiftStartsAt()
    {
        if (! $this->date_range) {
            return '';
        }

        $dates = $this->date_range ?? [];
        $startHours = array_map(function ($date) {
            return date('H:i', strtotime($date['start']));
        }, $dates);

        return min($startHours) ?? null;
    }

    public function shiftEndsAt()
    {
        if (! $this->date_range) {
            return '';
        }

        $dates = $this->date_range ?? [];
        $endHours = array_map(function ($date) {
            return date('H:i', strtotime($date['end']));
        }, $dates);

        return max($endHours) ?? null;
    }

    public function shiftHours()
    {
        $totalHours = 0;

        $shifts = $this->date_range;

        foreach ($shifts as $shift) {
            $startTime = new DateTime($shift['start']);
            $endTime = new DateTime($shift['end']);
            
            // Get difference in minutes
            $diffInMinutes = ($endTime->getTimestamp() - $startTime->getTimestamp()) / 60;
            
            // Convert to decimal hours (e.g., 1.5 hours for 1 hour and 30 minutes)
            $totalHours += $diffInMinutes / 60;
        }
        
        // Round to 2 decimal places
        return round($totalHours, 2);
    }

    /**
     * Get the invoices associated with this contract
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Calculate the outstanding amount for this contract
     * based on total_amount and paid invoices
     */
    public function calculateOutstandingAmount(): float
    {
        $paidInvoices = $this->invoices()->where('status', 'paid')->get();
        $totalPaid = $paidInvoices->sum('amount');
        
        $outstandingAmount = (float) ($this->total_amount - $totalPaid);

        return $outstandingAmount;
    }

    /**
     * Recalculate and update the payment status based on the outstanding amount
     * This is the centralized method for managing payment status
     * Also syncs the payment status with all related invoices for consistency
     * Updates the main contract status when payment is completed
     */
    public function recalculatePaymentStatus(): string
    {
        $outstandingAmount = $this->calculateOutstandingAmount();

        $newPaymentStatus = ($outstandingAmount <= 0.009) ? self::PAYMENT_STATUS_PAID : self::PAYMENT_STATUS_PENDING;

        if ($this->payment_status !== $newPaymentStatus) {
            $this->payment_status = $newPaymentStatus;

            // Update main contract status when payment is completed
            if ($newPaymentStatus === self::PAYMENT_STATUS_PAID) {
                $this->status = self::complete; // Use integer constant instead of string
            }

            $this->saveQuietly(); // Use saveQuietly to avoid re-triggering 'saving' model event if not desired

            $finalInvoiceStatusToSet = ($newPaymentStatus === self::PAYMENT_STATUS_PAID) ? 'completed' : 'pending';

            $this->invoices()->update([
                'status' => $finalInvoiceStatusToSet
            ]);
        }

        return $this->payment_status;
    }
}
