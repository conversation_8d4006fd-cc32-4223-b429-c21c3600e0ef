<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\MobileUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Notifications\GuestPasswordNotification;

class GuestLoginController extends Controller
{
    private function userObj($user)
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified' => false,
            'account_type' => '5', // Guest account type
            'status' => 1, // Changed to integer
            'profile' => [
                'name' => 'Guest User',
                'email' => $user->email,
                'account_type' => '5'
            ]
        ];
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        // Generate a random password
        $password = Str::random(12);

        // Create the user
        $user = MobileUser::create([
            'name' => 'Guest User',
            'email' => $request->email,
            'password' => Hash::make($password),
            'account_type' => '5',  // Guest account type
            'email_verified' => true,
            'email_verified_at' => now(),
            'status' => 1,
            'is_guest' => true
        ]);

        // Send password notification
        try {
            $user->notify(new GuestPasswordNotification($password));
        } catch (\Exception $e) {
            // Log the error but don't expose it to the user
            \Log::error('Failed to send guest password email: ' . $e->getMessage());
        }

        // Create access token
        $token = $user->createToken('guest-token')->accessToken;

        return response()->json([
            'error' => false,
            'message' => 'Guest login successful. Please check your email for your password.',
            'token' => $token,
            'data' => $this->userObj($user),
            'redirect' => '/search-operator'
        ]);
    }
} 