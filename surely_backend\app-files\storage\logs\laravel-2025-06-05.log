[2025-06-05 07:03:36] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-05 07:03:36] local.INFO: Total invoices found: 242  
[2025-06-05 07:03:36] local.INFO: Formatted data count: 242  
[2025-06-05 07:03:43] local.INFO: Displaying invoice #245 of type escrow  
