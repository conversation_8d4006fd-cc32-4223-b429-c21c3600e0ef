// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Button, TextField, Tabs, Divider, useToast, Image, Breadcrumbs } from 'reshaped';
import { useLocation, useNavigate } from 'react-router-dom';
import { paymentDetails, getPaymentDetails, addBankCardDetails, getBankCardDetails } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { headerLogo } from 'src/assets/images';
import { AppContext } from 'src/context/AppContext';
import { Elements, useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import StripePaymentForm from '../../StripePaymentForm';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

const ClientSettingsPaymentForm = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const redirectedState = location?.state
  const [activeTab, setActiveTab] = useState(redirectedState?.activeTab || '0');
  const toast = useToast();
  const { fetchAppData, name } = useContext(AppContext);

  const [utrNumber, setUtrNumber] = useState<string>('');
  const [vatNumber, setVatNumber] = useState<string>('');
  const [companyNumber, setCompanyNumber] = useState<string>('');

  const [cardHolderName, setCardHolderName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [expireDateMonth, setExpireDateMonth] = useState('');
  const [expireDateYear, setExpireDateYear] = useState('');
  const [cvvNumber, setCvvNumber] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [savedCards, setSavedCards] = useState([]);

  const containsOnlyNumbers = (value: any) => /^\d+$/.test(value);
  const padWithZero = (value: any) => (value < 10 ? `0${value}` : value);

  const handleExpireDateYearChange = (e: any) => {
    const inputValue = e.target.value.replace(/\D/g, '');
    const inputYear = parseInt(inputValue, 10) || 0;

    if (inputValue.length > 2) {
      setExpireDateYear(inputValue.slice(0, 2));
    } else {
      setExpireDateYear(inputValue);
    }
    if (inputValue.length === 2 && inputYear <= new Date().getFullYear() % 100) {
      setErrorMessage('Year must be greater than the last two digits of the current year.');
    }
  };

  const [isSaving, setIsSaving] = useState(false);

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const submitFirstTab = async () => {
    setIsSaving(true);
    const firstTabSettings: any = {
      vatNumber,
      companyNumber,
      utrNumber,
    };
    await paymentDetails(firstTabSettings)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  const submitBankCardTab = async () => {
    setIsSaving(true);
    const modifiedExpireDateYear = `20${expireDateYear}`;
    const bankCardTab = {
      cardNumber,
      expireDateMonth,
      expireDateYear: modifiedExpireDateYear,
      cvvNumber,
      cardHolderName,
    };
    await addBankCardDetails(bankCardTab)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  useEffect(() => {
    getPaymentDetails().then((data: any) => {
      const vat_number = data.data?.vat_number ? data.data?.vat_number : '';
      const company_number = data.data?.vat_number ? data.data?.vat_number : '';
      const utr_number = data.data?.vat_number ? data.data?.vat_number : '';

      setVatNumber(vat_number);
      setCompanyNumber(company_number);
      setUtrNumber(utr_number);
    });

    getBankCardDetails().then((data: any) => {
      if (Array.isArray(data?.data) && data?.data.length > 0) {
        const lastItem = data.data[data.data.length - 1];
        const formattedCardNumber = '**** **** **** ' + lastItem.last_numbers;
        const formattedCardName = lastItem.holder_name;
        setCardNumber(formattedCardNumber);
        setExpireDateMonth('* *');
        setExpireDateYear('* *');
        setCvvNumber('* * *');
        setCardHolderName(formattedCardName);
      } else {
        // Initialize with empty values for new card
        setCardNumber('');
        setExpireDateMonth('');
        setExpireDateYear('');
        setCvvNumber('');
        setCardHolderName('');
      }
    }).catch((error) => {
      console.error('Error fetching card details:', error);
      toast.show({
        title: 'Error',
        text: 'Failed to fetch card details. Please try again.',
        type: 'error'
      });
    });
  }, []);
  useEffect(() => {
    const fetchCards = async () => {
      try {
        const response = await getBankCardDetails();
        if (response?.data?.length) {
          setSavedCards(response.data);
        }
      } catch (error) {
        console.error('Failed to load saved cards:', error);
      }
    };
    fetchCards();
  }, []);


  const stripe = useStripe();
  const elements = useElements();

  const handleSubmit = async (event) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: 'card',
      card: elements.getElement(CardElement),
      billing_details: {
        name: cardHolderName,
      },
    });

    if (error) {
      toast.show({
        title: 'Error',
        text: error.message,
        type: 'error'
      });
    } else {
      // Send paymentMethod.id to your backend
      const response = await addBankCardDetails({
        payment_method_id: paymentMethod.id,
        holder_name: cardHolderName
      });
      
      if (response?.error) {
        toast.show({
          title: 'Error',
          text: 'Failed to save card details',
          type: 'error'
        });
      } else {
        toast.show({
          title: 'Success',
          text: 'Card details saved successfully',
          type: 'success'
        });
      }
    }
  };

  return (
    <View className='mx-auto w-full px-[12px] sm:px-0 sm:w-auto'>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/client-settings')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>Payment Details</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0'>
        <Text className='text-foreground-neutral font-rufina-stencil text-[#323C58] lg:text-[32px] xl:leading-10'>Payment details</Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          <Tabs.Item value='0'>
            <span className='rubik text-[14px] text-[#14171F]'>UTR</span>
          </Tabs.Item>
          <Tabs.Item value='1'>
            <span className='rubik text-[14px] text-[#14171F]'>Business details</span>
          </Tabs.Item>
          <Tabs.Item value='2'>
            <span className='rubik text-[14px] text-[#14171F]'>Bank card</span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-[16px] flex flex-col'>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>UTR number (optional)</Text>
              <TextField
                name='text'
                className='mt-[5px] h-[48px]  sm:w-[536px]'
                // placeholder={utrNumber}
                value={utrNumber}
                onChange={(e) => setUtrNumber(e.value)}
              />

            </View>
            <Divider className='mt-[16px] h-[1px] w-full'></Divider>

            <View className='mt-[16px] flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/client-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex sm:w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-[16px] flex flex-col'>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A] '>VAT number (optional)</Text>
              <TextField
                name='text'
                className='mt-[5px] h-[48px] sm:w-[536px]'
                placeholder='GB 992 8787 38'
                value={vatNumber}
                onChange={(e) => setVatNumber(e.value)}
              />
            </View>
            {/* <span className='my-4 text-base font-medium leading-6 text-[#323C58] underline'>Submit for validation</span> */}
            <View className='flex flex-col'>
              <Text className='text-neutral rubik mt-4 font-medium leading-4 text-[#1A1A1A]'>Company number (optional)</Text>
              <TextField
                name='text'
                className='mt-[5px] h-[48px]  sm:w-[536px]'
                placeholder='12345678'
                value={companyNumber}
                onChange={(e) => setCompanyNumber(e.value)}
              />
            </View>

            {/* <span className='my-4 text-base font-medium leading-6 text-[#323C58] underline'>Submit for validation</span> */}

            <Divider className='my-b mt-[16px] h-[1px] w-full'></Divider>

            <View className='mt-[16px] flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/client-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  submitFirstTab().then(() => {
                    toast.show({
                      title: 'Done!',
                      text: 'You have updated your profile',
                      startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                    });
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '2' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-[16px] flex flex-col'>
            {savedCards.length > 0 && (
              <View className='mb-6'>
                <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A] mb-3'>
                  Saved Cards
                </Text>
                <View className='flex flex-col gap-3'>
                  {savedCards.map((card: any) => (
                    <View 
                      key={card.id}
                      className='flex items-center p-4 border border-[#DFE2EA] rounded-lg'
                    >
                      <View className='flex-1'>
                        <Text className='text-[14px] text-[#14171F] font-medium'>
                          **** **** **** {card.last_numbers}
                        </Text>
                        <Text className='text-[12px] text-[#6B7280] mt-1'>
                          {card.holder_name} | {card.type}
                        </Text>
                      </View>
                      {/* Optional: Add delete card button */}
                      {/* <Button
                        variant='text'
                        onClick={() => handleDeleteCard(card.id)}
                        className='text-red-600'
                      >
                        Delete
                      </Button> */}
                    </View>
                  ))}
                </View>
              </View>
            )}
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A] '>Card holder name </Text>
              <TextField
                name='text'
                className='mt-[5px] h-[48px]  sm:w-[536px]'
                placeholder='Card holder name'
                value={cardHolderName}
                onChange={(e) => setCardHolderName(e.value)}
              />
            </View>
            <View className='mt-4'>
              <Elements stripe={stripePromise}>
                <StripePaymentForm cardHolderName={cardHolderName} />
              </Elements>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
    </View>
  );
};

const ClientSettingsPayment = () => {
  return (
    <Elements stripe={stripePromise}>
      <ClientSettingsPaymentForm />
    </Elements>
  );
};

export default ClientSettingsPayment;
