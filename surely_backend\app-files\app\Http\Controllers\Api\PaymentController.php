<?php

namespace App\Http\Controllers\Api;

use App\Events\NewMessageEvent;
use App\Http\Controllers\Controller;
use App\Http\Resources\ContractResource;
use App\Models\BankStripeAccount;
use App\Models\Card;
use App\Models\Chat;
use App\Models\Contract;
use App\Models\Invoice;
use App\Models\Job;
use App\Models\MobileUser;
use App\Models\Payment;
use App\Models\StripeAccount;
use App\Notifications\PaymentNotification;
use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Stripe\Account;
use Stripe\StripeClient;
use Illuminate\Support\Facades\Validator;
use Stripe\Payout;
use Stripe\Stripe;
use Stripe\Token;
use Stripe\Transfer;
use Stripe\PaymentIntent;

class PaymentController extends Controller
{
    protected $stripe;
    private const BUSINESS_PROFILE = [
        'mcc' => '5734', 
        'url' => 'https://surley.co.uk',
        'product_description' => 'Software'
    ];
    
    private const BUSINESS_SETTINGS = [
        'payments' => [
            'statement_descriptor' => 'SURLEY PAYMENT',
            'statement_descriptor_prefix' => 'SURLEY'
        ]
    ];

    public function __construct()
    {
        $stripeAccount = StripeAccount::first();
        if (!$stripeAccount) {
            throw new \Exception('Stripe configuration not found');
        }
        $this->stripe = new \Stripe\StripeClient($stripeAccount->secret);
    }

    public function index(Request $request): JsonResponse
    {
        $payments = Payment::where('receiver_id', auth()->id())->get();
        $contracts = ContractResource::collection($payments);
        return response()->json(['data' => $contracts]);
    }

    public function storeFromChat(Request $request, $chatId): JsonResponse
    {
        $chat = Chat::find($chatId);
        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ],401);
        }

        $request['client_id'] = $chat->sender_id;
        $request['operative_id'] = $chat->receiver_id;
        $request['chat_id'] = $chat->id;
        $contract = Contract::create($request->all());
        if(! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot created!',
                'data' => []
            ]);
        }
        $request['contract_id'] = $contract->id;
        $request['sender_id'] = $chat->sender_id;
        $request['receiver_id'] = $chat->receiver_id;

        $message = $chat->writeMessage($request);

        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return response()->json([
            'error' => false,
            'message' => 'Contract created successfully!',
            'data' => $contract
        ]);
    }

    public function store(Request $request, $contractId) {
        $request->validate([
            'client_id' => 'required',
            'operative_id' => 'required',
            'job_id' => 'required',
            'chat_id' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'payment_type' => 'required',
            'payment_amount' => 'required',
            'payment_status' => 'required',
            'payment_date' => 'required',
            'payment_method' => 'required',
            'payment_reference' => 'required',
            'payment_description' => 'required',
            'payment_image' => 'required',
            'status' => 'required',
        ]);
        $chatId = $request->get('chat_id');
        $chat = Chat::find($chatId);
        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ], 401);
        }
        $request['client_id'] = $chat->sender_id;
        $request['operative_id'] = $chat->receiver_id;
        $request['chat_id'] = $chat->id;

        $job = Job::create($request->all());
        if (! $job) {
            return response()->json([
                'error' => true,
                'message' => 'Job cannot created!',
                'data' => []
            ]);
        }

        $request['job_id'] = $job->id;

        $contract = Contract::create($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Contract created successfully!',
            'data' => $request->all()
        ]);
    }

    public function show($contractId): JsonResponse
    {
        $contract = Contract::find($contractId);
        return !$contract
            ? response()->json(['error' => true, 'message' => 'No contract found #'.$contractId.'!', 'data' => []])
            :response()->json([
                'error' => false,
                'message' => 'Data retrieved successfully!',
                'data' => new ContractResource($contract),
            ]);
    }

    public function update(Request $request, $contractId): JsonResponse
    {
        $contract = Contract::find($contractId);

        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }

        if (! $contract->update($request->all())) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot be updated!',
                'data' => [],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Contract updated successfully!',
            'data' => $contract,
        ]);
    }

    public function changeStatus(Request $request, $chatId, $contractId): JsonResponse
    {
        $chat = Chat::find($chatId);
        if (!$chat) {
            return response()->json([
                'error' => true,
                'message' => 'Chat session has expired!',
                'data' => []
            ],401);
        }

        $contract = Contract::find($contractId);
        if (! $contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found #'.$contractId.'!',
                'data' => [],
            ]);
        }

        if (! $contract->update($request->only('status'))) {
            return response()->json([
                'error' => true,
                'message' => 'Contract cannot updated!',
                'data' => [],
            ]);
        }

        $request['job_id'] = $contract->job_id;
        $request['contract_id'] = $contract->id;
        $request['sender_id'] = auth()->id();
        $request['receiver_id'] = auth()->id() == $chat->receiver_id ? $chat->sender_id : $chat->receiver_id;

        $message = $chat->writeMessage($request);
        broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();

        return  response()->json([
            'error' => false,
            'message' => 'Contract updated successfully!',
            'data' => $contract,
        ]);
    }

    public function save_utr_vat(Request $request) 
    {
        $user = auth()->user();
        
        if($request->filled('operator_vat')){
            $user->operator_vat = $request->get('operator_vat');
        }

        if ($request->filled('utr_number')) {
            $user->utr_number = $request->get('utr_number');
        }

        if(!$user->save()){
            return response()->json(['error' => true, 'message' => 'Operator VAT and UTR cannot be updated!', 'data' => array()]);
        }

        return response()->json([
            'error' => false,
            'message' => 'UTR and VAT updated successfully!',
        ]);
    }


    public function save_bank_account(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required',
                'routing_number' => 'required',
                'account_number' => 'required',
            ]);
    
            if ($validator->fails()) {
                return response()->json([
                    'error' => true,
                    'message' => $validator->errors()->first()
                ]);
            }
    
            $user = auth()->user();
    
            // 1. Create Connected Account with required capabilities
            try {
                $account = $this->stripe->accounts->create([
                    'type' => 'custom',
                    'country' => 'GB',
                    'email' => $user->email,
                    'capabilities' => [
                        'transfers' => ['requested' => true],
                        'card_payments' => ['requested' => true]
                    ],
                    'business_type' => 'individual',
                    'business_profile' => self::BUSINESS_PROFILE,
                    'settings' => self::BUSINESS_SETTINGS,
                    'individual' => [
                        'first_name' => explode(' ', $request->name)[0],
                        'last_name' => count(explode(' ', $request->name)) > 1 ? explode(' ', $request->name)[1] : '',
                        'email' => $user->email,
                        'phone' => '+' . preg_replace('/[^0-9]/', '', $user->phone),
                        'address' => [
                            'city' => $user->city ?? 'London',
                            'country' => 'GB',
                            'line1' => $user->address ?? $user->address_1,
                            'postal_code' => preg_replace('/\s+/', '', $user->postal_code)
                        ],
                        'dob' => [
                            'day' => 1,
                            'month' => 1,
                            'year' => 1990
                        ]
                    ],
                    'tos_acceptance' => [
                        'date' => time(),
                        'ip' => $request->ip()
                    ]
                ]);

                // Verify account creation was successful
                if (!$account->capabilities['transfers'] === 'active') {
                    throw new \Exception('Account creation failed: transfers capability not active');
                }

            } catch (\Exception $e) {
                // Handle error
                return response()->json([
                    'error' => true,
                    'message' => $e->getMessage()
                ]);
            }
    
            // 2. Create external bank account
            $bankAccount = $this->stripe->accounts->createExternalAccount(
                $account->id,
                [
                    'external_account' => [
                        'object' => 'bank_account',
                        'country' => 'GB',
                        'currency' => 'gbp',
                        'account_holder_name' => $request->name,
                        'account_holder_type' => 'individual',
                        'routing_number' => $request->routing_number,
                        'account_number' => $request->account_number,
                    ]
                ]
            );
    
            // 3. Save to database
            $bankStripeAccount = BankStripeAccount::updateOrCreate(
                ['mobile_user_id' => $user->id],
                [
                    'name' => $request->name,
                    'stripe_id' => $account->id,
                    'token_id' => $bankAccount->id,
                    'type' => 'custom',
                    'last_digits' => substr($request->account_number, -4)
                ]
            );
    
            // Update the user's sort code and account number
            $user->update([
                'sort_code' => $request->routing_number,
                'account_number' => $request->account_number
            ]);
    
            // Enable capabilities after account creation
            $this->stripe->accounts->update(
                $account->id,
                [
                    'capabilities' => [
                        'transfers' => ['requested' => true],
                        'card_payments' => ['requested' => true],
                        'tax_reporting_us_1099_k' => ['requested' => false],
                    ],
                ]
            );
    
            return response()->json([
                'error' => false,
                'message' => 'Bank account saved successfully!'
            ]);
    
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function get_bank_account()
    {
        try {
            $user = auth()->user();
            $transporterStripeAccount = BankStripeAccount::query()
                ->select('name', 'last_digits')
                ->where('mobile_user_id', auth()->id())
                ->first();

            if (!$transporterStripeAccount) {
                return response()->json([
                    'error' => true,
                    'message' => 'This user has no bank account!',
                ]);
            }

            // Add sort code to the response
            $transporterStripeAccount->sort_code = $user->sort_code;
            
            return response()->json([
                'error' => false,
                'data' => $transporterStripeAccount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ]);
        }
    }

   public static function calculateTotalPayment($shifts, $hourlyRate)
    {
        $totalPayment = 0;

        foreach ($shifts as $shift) {
            $startTime = new DateTime($shift['start']);
            $endTime = new DateTime($shift['end']);
            
            // Calculate duration in minutes
            $diffInMinutes = ($endTime->getTimestamp() - $startTime->getTimestamp()) / 60;
            
            // Convert to decimal hours and calculate payment
            $decimalHours = $diffInMinutes / 60;
            $paymentForShift = $decimalHours * $hourlyRate;
            
            // Add the payment for the current shift to the total payment
            $totalPayment += $paymentForShift;
        }
        
        return round($totalPayment, 2);
    }

    public function payContract(Request $request, $id)
    {
        $contract = Contract::find($id);

        if($contract->client_id != auth()->id()){
            return response()->json([
                'error' => true,
                'message' => 'Contract not Authorized!',
                'data' => []
            ]);
        }

        if (!$contract) {
            return response()->json([
                'error' => true,
                'message' => 'Contract not found!',
                'data' => []
            ]);
        }

        if ($contract->payment_status == Contract::PAYMENT_STATUS_PAID) {
            return response()->json([
                'error' => true,
                'message' => 'Contract already paid!',
                'data' => []
            ]);
        }

        if($request->type != 'escrow_payment'){
            if($contract->shifts_status != Contract::SHIFT_STATUS_COMPLETED){
                return response()->json([
                    'error' => true,
                    'message' => 'Shifts not completed!',
                    'data' => []
                ]);
            }
            if($contract->escrow_status != Contract::PAYMENT_STATUS_PAID){
                return response()->json([
                    'error' => true,
                    'message' => 'Escrow not paid!',
                    'data' => []
                ]);
            }
        }

        DB::beginTransaction();

        $invoice = new Invoice();
        $invoice->contract_id = $contract->id;

        if($request->type == 'escrow_payment'){
            $subTotalAmount = PaymentController::calculateTotalPayment($contract->date_range, $contract->hourly_rate);

            $emergency_hire = $contract->job?->is_emergency_hire ?? false;
            $emergency_hire_fee_amount = 0;
            $emergency_hire_fee_rate = 0;

            if($emergency_hire){
                $emergency_hire_fee_rate = Contract::EMERGENCY_HIRE_FEE_RATE;
                $emergency_hire_fee_amount = $subTotalAmount * (Contract::EMERGENCY_HIRE_FEE_RATE / 100);
                $subTotalAmount += $emergency_hire_fee_amount;
            }

            $operatorVatRate = MobileUser::find($contract->operative_id)->operator_vat ?? 0;

            $operatorVatAmount = 0;
            if($operatorVatRate > 0) {
                $operatorVatAmount = $subTotalAmount * ($operatorVatRate / 100);
            }

            $totalAmountWithoutAppFee = $subTotalAmount + $operatorVatAmount;

            $appFee = 0;
            $appFeeVat = 0;
            $appFeeTotal = 0;
            $appFeeRate = 0;
            $appVatRate = 0;

            $transactionFeeAmount = 0;
            $transactionFeeRate = 0;

            if($contract->client->created_at->format('Y-m-d H:i:s') > now()->subMonth()->format('Y-m-d H:i:s')) {
                $transactionFeeAmount = $totalAmountWithoutAppFee * (Contract::TRANSACTION_FEE_RATE / 100);
                $transactionFeeRate = Contract::TRANSACTION_FEE_RATE;
            }else {
                $appFeeRate = Contract::APPLICATION_FEE_RATE;
                $appFee = $totalAmountWithoutAppFee * ($appFeeRate / 100);
                $appVatRate = Contract::APPLICATION_VAT_RATE;
                $appFeeVat = $appFee * ($appVatRate / 100);
                $appFeeTotal = $appFee + $appFeeVat;
            }

            $totalAmount = $totalAmountWithoutAppFee + $appFeeTotal + $transactionFeeAmount;

            $escrowAmount = $totalAmount * (MobileUser::find($contract->client_id)->getEscrowDeposit() / 100);

            $contract->update([
                'application_vat_rate' => $appVatRate,
                'application_vat_amount' => $appFeeVat,
                'application_fee_amount' => $appFee,
                'application_fee_rate' => $appFeeRate,
                'sub_total' => $subTotalAmount - $emergency_hire_fee_amount,
                'escrow_amount' => $escrowAmount,
                'total_amount' => $totalAmount,
                'operator_vat_amount' => $operatorVatAmount,
                'operator_vat_rate' => $operatorVatRate,
                'escrow_rate' => MobileUser::find($contract->client_id)->getEscrowDeposit(),
                'emergency_hire_fee_amount' => $emergency_hire_fee_amount,
                'emergency_hire_fee_rate' => $emergency_hire_fee_rate,
                'payment_transaction_fee_rate' => $transactionFeeRate,
                'payment_transaction_fee_amount' => $transactionFeeAmount,
            ]);

            $invoice->amount = $escrowAmount;
            $invoice->sub_amount = $subTotalAmount - $emergency_hire_fee_amount;
            $invoice->type = Invoice::TYPE_ESCROW;
            $invoice->application_vat_rate = $appVatRate;
            $invoice->application_vat_amount = $appFeeVat;
            $invoice->operator_vat_rate = $operatorVatRate;
            $invoice->operator_vat_amount = $operatorVatAmount;
            $invoice->application_fee_rate = $appFeeRate;
            $invoice->application_fee_amount = $appFee;
        } else {
            if($contract->shifts_status != Contract::SHIFT_STATUS_COMPLETED){
                DB::rollBack();

                return response()->json([
                    'error' => true,
                    'message' => 'Shifts not completed!',
                    'data' => []
                ]);
            }

            $invoice->amount = $contract->total_amount - $contract->escrow_amount;
            $invoice->sub_amount = $contract->sub_total * ((100 - $contract->escrow_rate) / 100);
            $invoice->type = Invoice::TYPE_PAYMENT;
            $invoice->application_vat_rate = $contract->application_vat_rate;
            $invoice->application_vat_amount = $contract->application_vat_amount * ((100 - $contract->escrow_rate) / 100);
            $invoice->operator_vat_rate = $contract->operator_vat_rate;
            $invoice->operator_vat_amount = $contract->operator_vat_amount * ((100 - $contract->escrow_rate) / 100);
            $invoice->application_fee_rate = $contract->application_fee_rate;
            $invoice->application_fee_amount = $contract->application_fee_amount * ((100 - $contract->escrow_rate) / 100);
        }

        $invoice->currency = 'GBP';
        $invoice->operator_id = $contract->operative_id;
        $invoice->client_id = $contract->client_id;
        $invoice->status = Invoice::STATUS_PENDING;

        $cardId = $request->card_id;

        $card = Card::find($cardId);

        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => round(($contract->total_amount - $contract->escrow_amount) * 100),
                'currency' => 'gbp',
                'customer' => $card->customer_id,
                'payment_method' => $request->payment_method_id,
                'confirm' => true,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never'
                ]
            ]);

            if ($paymentIntent->status !== 'succeeded') {
                throw new \Exception('Payment failed');
            }
        } catch (\Stripe\Exception\CardException $e) {
            // Handle card errors
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            // Handle invalid parameters
        }

        $invoice->stripe_id = $paymentIntent->id;
        $invoice->stripe_customer_id = $paymentIntent->customer;
        $invoice->stripe_payment_intent_id = $paymentIntent->id;
        $invoice->stripe_payment_intent_client_secret = $paymentIntent->client_secret;
        $invoice->stripe_payment_intent_status = $paymentIntent->status;
        $invoice->stripe_payment_invoice_id = $paymentIntent->invoice;
        if($paymentIntent->status == 'succeeded'){
           if($request->type == 'escrow_payment'){
               if(MobileUser::find($contract->client_id)->getEscrowDeposit() == 100){
                   try {
                       if(!BankStripeAccount::where('mobile_user_id', $contract->operative_id)->first()?->stripe_id ){
                           DB::rollBack();

                           return response()->json([
                               'error' => true,
                               'message' => 'Operative bank account not found!',
                               'data' => []
                           ]);
                       }
                       $transfer = Transfer::create([
                           "amount" => round(($contract->sub_total + $contract->operator_vat_amount) * 100,0),
                           "currency" => "gbp",
                           "destination" => BankStripeAccount::where('mobile_user_id', $contract->operative_id)->first()->stripe_id,
                       ]);
            if($transfer->reversed == false || $transfer->amount_reversed == 0){
                $contract->update([
                    'payment_status' => Contract::PAYMENT_STATUS_PAID,
                    'status' => Contract::complete  // Update main status to complete (integer)
                ]);
                $invoice->status = 'paid'; // Set invoice status to paid immediately for 100% escrow
                       } else {
                           DB::rollBack();

                           return response()->json([
                               'error' => true,
                               'message' => 'Payment failed!',
                               'data' => []
                           ]);
                       }
                   } catch (\Exception $e){

                       DB::rollBack();
                       return response()->json([
                           'error' => true,
                           'message' => $e->getMessage(),
                           'data' => []
                       ]);
                   }
               }

               $contract->update(['escrow_status' => Contract::PAYMENT_STATUS_PAID, 'escrow_amount' => $invoice->amount]);
           } else {
               try {
                   if(!BankStripeAccount::where('mobile_user_id', $contract->operative_id)->first()?->stripe_id ){
                       DB::rollBack();

                        return response()->json([
                            'error' => true,
                            'message' => 'Operative bank account not found!',
                            'data' => []
                          ]);
                   }
                   $transfer = Transfer::create([
                       "amount" => round(($contract->sub_total + $contract->operator_vat_amount) * 100,0),
                       "currency" => "gbp",
                       "destination" => BankStripeAccount::where('mobile_user_id', $contract->operative_id)->first()->stripe_id,
                   ]);
                   if($transfer->reversed == false || $transfer->amount_reversed == 0){
                $contract->update([
                    'payment_status' => Contract::PAYMENT_STATUS_PAID,
                    'status' => Contract::complete  // Update main status to complete (integer)
                ]);
                   } else {
                       DB::rollBack();

                       return response()->json([
                           'error' => true,
                           'message' => 'Payment failed!',
                           'data' => []
                       ]);
                   }
               } catch (\Exception $e){

                   DB::rollBack();
                   return response()->json([
                       'error' => true,
                       'message' => $e->getMessage(),
                       'data' => []
                   ]);
               }
           }

           $invoice->update(['payment_status' => Contract::PAYMENT_STATUS_PAID]);
        }else{
            DB::rollBack();

            return response()->json([
                'error' => true,
                'message' => 'Payment failed!',
                'data' => []
            ]);
        }

        if($invoice->save()){
            // Recalculate contract payment status and main status after payment
            $contract->recalculatePaymentStatus();

            DB::commit();

            $chat = Chat::find($contract->chat_id);

            if ($chat) {
                $request['contract_id'] = $contract->id;
                $request['sender_id'] = auth()->id();
                $request['receiver_id'] = auth()->id() == $chat->receiver_id ? $chat->sender_id : $chat->receiver_id;

                $message = $chat->writeMessage($request);

                $operator = MobileUser::find($request['receiver_id']);
                if ($operator->notification('new_chat_message_mail')) {
                    $operator->notify(new PaymentNotification($operator->name));
                }
                broadcast(new NewMessageEvent($message->load('receiver')))->toOthers();
            }

            return response()->json([
                'error' => false,
                'message' => 'Payment successful!',
                'data' => new ContractResource($contract),
            ]);
        }
        DB::rollBack();

        return response()->json([
            'error' => true,
            'message' => 'Payment failed!',
            'data' => []
        ]);

    }

    public function pay(Request $request)
    {
        try {
            $stripeAccount = StripeAccount::first();
            Stripe::setApiKey($stripeAccount->secret);
            
            $contract = Contract::findOrFail($request->contract_id);
            $card = Card::where('mobile_user_id', auth()->id())->first();
            
            if (!$card) {
                throw new \Exception('No card found');
            }

            // Create invoice with all required fields
            $invoice = new Invoice();
            $invoice->contract_id = $contract->id;
            $invoice->currency = 'GBP';
            $invoice->operator_id = $contract->operative_id;
            $invoice->client_id = $contract->client_id;
            $invoice->status = Invoice::STATUS_PENDING;
            
            if ($request->is_outstanding) {
                $amount = $contract->total_amount - $contract->escrow_amount ;
                $invoice->type = Invoice::TYPE_PAYMENT;
                $invoice->sub_amount = $contract->sub_total * ((100 - $contract->escrow_rate) / 100);
                $invoice->application_vat_rate = $contract->application_vat_rate;
                $invoice->application_vat_amount = $contract->application_vat_amount * ((100 - $contract->escrow_rate) / 100);
                $invoice->operator_vat_rate = $contract->operator_vat_rate;
                $invoice->operator_vat_amount = $contract->operator_vat_amount * ((100 - $contract->escrow_rate) / 100);
                $invoice->application_fee_rate = $contract->application_fee_rate;
                $invoice->application_fee_amount = $contract->application_fee_amount * ((100 - $contract->escrow_rate) / 100);
            } else {
                $amount = $contract->escrow_amount;
                $invoice->type = Invoice::TYPE_ESCROW;
                $invoice->sub_amount = $contract->sub_total * ($contract->escrow_rate / 100);
                $invoice->application_vat_rate = $contract->application_vat_rate;
                $invoice->application_vat_amount = $contract->application_vat_amount * ($contract->escrow_rate / 100);
                $invoice->operator_vat_rate = $contract->operator_vat_rate;
                $invoice->operator_vat_amount = $contract->operator_vat_amount * ($contract->escrow_rate / 100);
                $invoice->application_fee_rate = $contract->application_fee_rate;
                $invoice->application_fee_amount = $contract->application_fee_amount * ($contract->escrow_rate / 100);
            }

            $invoice->amount = $amount;

            $paymentIntent = PaymentIntent::create([
                'amount' => round($amount * 100),
                'currency' => 'gbp',
                'customer' => $card->customer_id,
                'payment_method' => $request->payment_method_id,
                'confirm' => true,
                'automatic_payment_methods' => [
                    'enabled' => true,
                    'allow_redirects' => 'never'
                ]
            ]);

            if ($paymentIntent->status === 'succeeded') {
                if ($request->is_outstanding) {
                    $contract->update([
                        'payment_status' => Contract::PAYMENT_STATUS_PAID
                    ]);
                    $this->payOperative($contract);
                } else {
                    $contract->update([
                        'escrow_status' => Contract::ESCROW_STATUS_PAID
                    ]);
                }

                $invoice->stripe_payment_intent_id = $paymentIntent->id;
                $invoice->stripe_payment_intent_status = $paymentIntent->status;
                $invoice->payment_status = Contract::PAYMENT_STATUS_PAID;
                $invoice->status = 'paid'; // Set invoice status to paid
                $invoice->save();

                // Recalculate contract payment status and main status after payment
                $contract->recalculatePaymentStatus();

                return response()->json([
                    'error' => false,
                    'message' => 'Payment successful',
                    'data' => new ContractResource($contract)
                ]);
            }

            throw new \Exception('Payment failed');

        } catch (\Exception $e) {
            \Log::error('Payment failed', [
                'error' => $e->getMessage()
            ]);
            
            return response()->json([
                'error' => true,
                'message' => $e->getMessage(),
                'data' => []
            ]);
        }
    }

    private function payOperative(Contract $contract) {
        try {
            // Get operative's bank account
            $operativeBankAccount = BankStripeAccount::where('mobile_user_id', $contract->operative_id)->first();
    
            if (!$operativeBankAccount?->stripe_id) {
                throw new \Exception('Operative bank not found!');
            }
    
            // Calculate amount to transfer
            $transferAmount = round(($contract->sub_total + $contract->operator_vat_amount) * 100);
    
            // Create payout to operative's card
            $payout = \Stripe\Transfer::create([
                "amount" => $transferAmount,
                "currency" => "gbp",
                "destination" => $operativeBankAccount->stripe_id
            ]);
    
            if ($payout->status === 'failed') {
                throw new \Exception('Payout to operative card failed');
            }
    
            return true;
    
        } catch (\Exception $e) {
            \Log::error('Failed to pay operative', [
                'contract_id' => $contract->id,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

/**
     * Handle successful payment intents from Stripe webhook.
     *
     * @param \Stripe\PaymentIntent $paymentIntent
     * @return void
     */
    private function handlePaymentSuccess($paymentIntent)
    {
        $this->updatePaymentStatus($paymentIntent);
    }
    public function handleStripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        
        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload, $sig_header, config('services.stripe.webhook_secret')
            );
            
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $this->handlePaymentSuccess($event->data->object);
                    break;
                case 'payment_intent.payment_failed':
                    $this->handlePaymentFailure($event->data->object);
                    break;
                case 'transfer.succeeded':
                    $this->handleTransferSuccess($event->data->object);
                    break;
                case 'transfer.failed':
                    $this->handleTransferFailure($event->data->object);
                    break;
                case 'charge.dispute.created':
                    $this->handleDispute($event->data->object);
                    break;
            }
            
            return response()->json(['status' => 'success']);
        } catch (\UnexpectedValueException $e) {
            return response()->json(['error' => 'Invalid payload'], 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            return response()->json(['error' => 'Invalid signature'], 400);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    public function updatePaymentStatus($paymentIntent)
    {
        $invoice = Invoice::where('stripe_payment_intent_id', $paymentIntent->id)->first();

        if (!$invoice) {
            \Log::error('Invoice not found for payment intent: ' . $paymentIntent->id);
            return;
        }

        switch ($paymentIntent->status) {
            case 'succeeded':
                $invoice->update(['status' => 'paid']);
                // Trigger contract status recalculation when payment succeeds
                $contract = $invoice->contract;
                if ($contract) {
                    $contract->recalculatePaymentStatus();
                }
                break;
            case 'requires_payment_method':
                $invoice->update(['status' => 'failed']);
                break;
            case 'requires_action':
                $invoice->update(['status' => 'pending_authentication']);
                break;
        }
    }

    public function deleteStripeAccount($accountId)
    {
        try {
            $stripeAccount = StripeAccount::first();
            if (!$stripeAccount) {
                return response()->json([
                    'error' => true,
                    'message' => 'Stripe configuration not found'
                ], 404);
            }

            $stripe = new \Stripe\StripeClient($stripeAccount->secret);
            $stripe->accounts->delete($accountId, []);

            // If you're also storing the account in your database, you might want to delete it here
            $bankStripeAccount = BankStripeAccount::where('stripe_id', $accountId)->first();
            if ($bankStripeAccount) {
                $bankStripeAccount->delete();
            }

            return response()->json([
                'error' => false,
                'message' => 'Stripe account deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteMultipleStripeAccounts(Request $request)
    {
        try {
            $stripeAccount = StripeAccount::first();
            if (!$stripeAccount) {
                return response()->json([
                    'error' => true,
                    'message' => 'Stripe configuration not found'
                ], 404);
            }

            $accountIds = $request->account_ids;
            $results = [];

            foreach ($accountIds as $accountId) {
                try {
                    $this->stripe->accounts->delete($accountId, []);
                    
                    // Delete from database if exists
                    $bankStripeAccount = BankStripeAccount::where('stripe_id', $accountId)->first();
                    if ($bankStripeAccount) {
                        $bankStripeAccount->delete();
                    }

                    $results[$accountId] = [
                        'success' => true,
                        'message' => 'Deleted successfully'
                    ];
                } catch (\Exception $e) {
                    $results[$accountId] = [
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'error' => false,
                'message' => 'Bulk deletion completed',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getAllStripeAccounts()
    {
        try {
            $stripeAccount = StripeAccount::first();
            if (!$stripeAccount) {
                return response()->json([
                    'error' => true,
                    'message' => 'Stripe configuration not found'
                ], 404);
            }

            $accounts = $this->stripe->accounts->all(['limit' => 100]);
            $accountIds = [];
            $hasMore = true;
            $lastId = null;

            while ($hasMore) {
                $params = ['limit' => 100];
                if ($lastId) {
                    $params['starting_after'] = $lastId;
                }

                $accounts = $this->stripe->accounts->all($params);
                
                foreach ($accounts->data as $account) {
                    $accountIds[] = [
                        'id' => $account->id,
                        'email' => $account->email,
                        'created' => date('Y-m-d H:i:s', $account->created)
                    ];
                    $lastId = $account->id;
                }
                
                $hasMore = $accounts->has_more;
            }

            return response()->json([
                'error' => false,
                'message' => 'Accounts retrieved successfully',
                'data' => $accountIds
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getRestrictedAccounts()
    {
        try {
            $stripeAccount = StripeAccount::first();
            if (!$stripeAccount) {
                return response()->json([
                    'error' => true,
                    'message' => 'Stripe configuration not found'
                ], 404);
            }

            $accounts = $this->stripe->accounts->all([
                'limit' => 100,
                'starting_after' => null // We'll use this for pagination
            ]);
            
            $restrictedAccounts = [];
            $hasMore = true;
            
            while ($hasMore) {
                foreach ($accounts as $account) {
                    if ($account->requirements->disabled_reason !== null) {
                        $restrictedAccounts[] = [
                            'id' => $account->id,
                            'email' => $account->email,
                            'created' => date('Y-m-d H:i:s', $account->created),
                            'disabled_reason' => $account->requirements->disabled_reason,
                            'requirements' => $account->requirements->currently_due
                        ];
                    }
                }
                
                if ($accounts->has_more) {
                    $lastAccount = end($accounts->data);
                    $accounts = $this->stripe->accounts->all([
                        'limit' => 100,
                        'starting_after' => $lastAccount->id
                    ]);
                } else {
                    $hasMore = false;
                }
            }

            return response()->json([
                'error' => false,
                'message' => 'Restricted accounts retrieved successfully',
                'data' => $restrictedAccounts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ], 500);
        }
    }

}
