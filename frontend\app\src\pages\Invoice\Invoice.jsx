import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useReactToPrint } from 'react-to-print';

import { getContractInvoice } from 'src/services/contracts';
import SurelyHeaderIcon from 'src/components/Header/SurelyHeaderIcon/SurelyHeaderIcon';
import Loading from 'src/components/Loading/Loading';
import SurelyIconNoData from 'src/components/NoData/SurelyIconNoData';

const Invoice = () => {
  const params = useParams();
  const [invoice, setInvoice] = useState();
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(true);

  const contract = invoice?.contract;
  const client = invoice?.client;
  const operative = invoice?.operative;
  const invoiceOutstanding = invoice?.invoiceOutstanding;
  const isEmergencyHire = !!contract?.emergency_hire_fee_amount;
  const emergencyHireFee = Number(contract?.emergency_hire_fee_amount || 0);
  const emergencyHireRate = Number(contract?.emergency_hire_fee_rate || 0);

  const applicationFeeAmount = Number(contract?.application_fee_amount || 0);
  const applicationVatRate = 20; 
  const applicationVatAmount = Number(contract?.application_vat_amount || (applicationFeeAmount * 0.2));
  const transactionFeeAmount = Number(contract?.payment_transaction_fee_amount || 0);

  const allServiceFees = transactionFeeAmount || applicationFeeAmount + applicationVatAmount;

  const surelyFeeTotal = allServiceFees + applicationVatAmount;

  const componentRef = useRef();
  const buttonRef = useRef();
  const handlePrint = useReactToPrint({
    onBeforePrint: () => {
      buttonRef.current.className = 'hidden';
    },
    content: () => {
      buttonRef.current.className = 'hidden';
      return componentRef.current;
    },
    onAfterPrint: () => {
      buttonRef.current.className =
        'mb-10 text-md mx-auto mt-10 w-fit cursor-pointer rounded border border-gray-300 bg-gray-200 px-4 py-2 font-medium md:mt-20 lg:mt-32';
    },
  });

  useEffect(() => {
    setIsLoadingInvoice(true);
    getContractInvoice(params.id).then((res) => {
      setInvoice(res);
      setIsLoadingInvoice(false);
    });
  }, [params.id]);

  if (isLoadingInvoice) {
    return (
      <div className=''>
        <Loading />
      </div>
    );
  }

  // Calculate base amounts
  const subtotal = Number(contract?.sub_total || 0);
  const vatRate = Number(contract?.operator_vat_rate || 0);
  const vatAmount = Number(contract?.operator_vat_amount || 0);
  const serviceFeeRate = Number(contract?.application_fee_rate || 0);
  const serviceFeeAmount = Number(contract?.application_fee_amount || 0);
  const escrowRate = Number(contract?.escrow_rate || 0);

  // Calculate total before escrow
  const totalAmount = Number(contract?.total_amount || 0);

  // Calculate escrow amount (10% of total)
  const escrowAmount = (totalAmount * escrowRate) / 100;

  // Calculate outstanding amount (remaining 90%)
  const outstandingAmount = totalAmount - escrowAmount;

  return (
    <article ref={componentRef}>
      <div className='rubik mx-auto flex h-full w-full max-w-[1260px] flex-col gap-4 px-6 text-black xl:px-0'>
        <div className='mt-10 flex justify-between md:mt-20 lg:mt-32'>
          <SurelyHeaderIcon />
          <div className='text-right'>
            <h1 className='font-rubik text-3xl font-semibold'>
              Invoice <br /> #{contract?.id}
            </h1>
            <div className=''>
              <div className=''>
                <h3 className='text-lg font-medium'>Issue date:</h3>
                <div className='text-md flex flex-col gap-4'>{contract?.issue_date}</div>
              </div>
              <div className=''>
                <h3 className='text-lg font-medium'>Due date:</h3>
                <div className='text-md flex flex-col gap-4'>{contract?.due_date}</div>
              </div>
            </div>
          </div>
        </div>
        <hr className='opacity-60' />
        <div className='flex flex-col gap-4'>
          <h4 className='text-lg font-medium text-gray-800'>Base Charges</h4>
          <hr className='opacity-60' />
          <div className='grid w-full grid-cols-4 items-center justify-between'>
            <div className='text-md col-span-2 sm:col-span-1'>Subtotal</div>
            <div className='text-md'>{`${contract?.shift_hours} * ${contract?.hourly_rate}/h`}</div>
            <div className='text-md text-right sm:col-span-2'>{`£${+contract?.sub_total}`}</div>
          </div>
          <div className='grid w-full grid-cols-4 items-center justify-between'>
            <div className='text-md col-span-2 sm:col-span-1'>VAT</div>
            <div className='text-md'>{`${contract?.operator_vat_rate ?? 0}%`}</div>
            <div className='text-md text-right sm:col-span-2'>{`£${Number(contract?.operator_vat_amount).toFixed(2) ?? 0}`}</div>
          </div>
          <hr className='opacity-60' />

          <h4 className='text-lg font-medium text-gray-800'>Surely Fees</h4>
          <hr className='opacity-60' />
          {isEmergencyHire && emergencyHireFee > 0 && (
            <div className='grid w-full grid-cols-4 items-center justify-between'>
              <div className='text-md col-span-2 sm:col-span-1'>Emergency hire fee</div>
              <div className='text-md'>{`${emergencyHireRate}%`}</div>
              <div className='text-md text-right sm:col-span-2'>{`£${emergencyHireFee.toFixed(2)}`}</div>
            </div>
          )}
          {surelyFeeTotal > 0 && (
            <>
              {allServiceFees > 0 && (
                <div className='grid w-full grid-cols-4 items-center justify-between'>
                  <div className='text-md col-span-2 sm:col-span-1'>Service fees</div>
                  <div className='text-md'>{`${!!contract?.payment_transaction_fee_amount ? contract?.payment_transaction_fee_rate + '%' : (contract?.application_fee_rate + '% + VAT' ?? null)}`}</div>
                  <div className='text-md text-right sm:col-span-2'>{`£${Number(allServiceFees)?.toFixed(2)}`}</div>
                </div>
              )}
              {applicationVatAmount > 0 && (
                <div className='grid w-full grid-cols-4 items-center justify-between'>
                  <div className='text-md col-span-2 sm:col-span-1'>VAT</div>
                  <div className='text-md'>{`${applicationVatRate}%`}</div>
                  <div className='text-md text-right sm:col-span-2'>{`£${applicationVatAmount.toFixed(2)}`}</div>
                </div>
              )}
              {/* <div className='grid w-full grid-cols-4 items-center justify-between'>
                <div className='text-md col-span-2 sm:col-span-1'>Surely Fee</div>
                <div className='text-md'>20% + VAT</div>
                <div className='text-md text-right sm:col-span-2'>{`£${surelyFeeTotal.toFixed(2)}`}</div>
              </div> */}
            </>
          )}
          <hr className='opacity-60' />

          <h4 className='text-lg font-medium text-gray-800'>Payment Structure</h4>
          <hr className='opacity-60' />
          <div className='grid w-full grid-cols-4 items-center justify-between'>
            <div className='text-md col-span-2 sm:col-span-1'>Escrow amount</div>
            <div className='text-md'>{`${escrowRate}%`}</div>
            <div className='text-md text-right sm:col-span-2'>{`£${escrowAmount.toFixed(2)}`}</div>
          </div>
          <div className='grid w-full grid-cols-4 items-center justify-between'>
            <div className='text-md col-span-2 sm:col-span-1'>Outstanding amount</div>
            <div className='text-md'>{`${100 - escrowRate}%`}</div>
            <div className='text-md text-right sm:col-span-2'>{`£${outstandingAmount.toFixed(2)}`}</div>
          </div>
          <hr className='opacity-60' />
          
          <div className='mt-4 bg-gray-100 p-4 rounded-lg'>
            <div className='grid w-full grid-cols-4 items-center justify-between'>
              <h4 className='col-span-2 text-xl font-bold sm:col-span-1'>Total</h4>
              <div className='text-xl font-bold col-span-2 text-right sm:col-span-3'>{`£${Number(contract?.total_amount)?.toFixed(2) ?? 0}`}</div>
            </div>
          </div>
        </div>
        <div className='mt-10 flex items-center justify-between md:mt-14 lg:mt-20'>
          <div className='space-y-3'>
            <h4 className='text-lg font-medium'>Question</h4>
            <div className='text-md'>Surely</div>
            <div className='text-md'><EMAIL></div>
            <div className='text-md'>+44 (0) 20 3582 5523</div>
          </div>
          <SurelyIconNoData width={100} height={100} />
        </div>
        <div
          onClick={handlePrint}
          ref={buttonRef}
          className='text-md mx-auto mb-10 mt-10 w-fit cursor-pointer rounded border border-gray-300 bg-gray-200 px-4 py-2 font-medium md:mt-20 lg:mt-32'
        >
          Print
        </div>
      </div>
    </article>
  );
};

export default Invoice;
