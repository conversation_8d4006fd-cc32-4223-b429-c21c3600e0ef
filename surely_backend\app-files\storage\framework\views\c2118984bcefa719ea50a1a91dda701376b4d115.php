

<?php $__env->startSection('content'); ?>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-body">
                        <h2 class="card-title mb-4">Create Payment</h2>
                        <form id="payment-form">
                            <?php echo csrf_field(); ?>
                            <div class="d-flex justify-content-between">
                                <div class="mb-3 col-md-5 p-0">
                                    <label for="amount" class="form-label">Amount (in cents)</label>
                                    <input type="number" class="form-control" id="amount" name="amount" required>
                                </div>
                                <div class="mb-3 col-md-5 p-0">
                                    <label for="currency" class="form-label">Currency</label>
                                    <select class="form-control" id="currency" name="currency">
                                        <option value="gbp">GBP</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div id="card-element" class="">
                                    <!-- A Stripe Element will be inserted here. -->
                                    <!-- Card information inputs -->
                                </div>
                            </div>
                            <div id="card-errors" role="alert" class="alert alert-danger d-none"></div>
                            <button type="submit" class="btn btn-primary w-100 mt-3">Pay</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        const stripe = Stripe(`<?php echo e(\App\Models\StripeAccount::first()->key); ?>`);
        const elements = stripe.elements();
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        const form = document.getElementById('payment-form');
        const cardErrors = document.getElementById('card-errors');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            const {
                paymentMethod,
                error
            } = await stripe.createPaymentMethod('card', cardElement);

            if (error) {
                cardErrors.textContent = error.message;
                cardErrors.classList.remove('d-none');
            } else {
                cardErrors.classList.add('d-none');
                // Send paymentMethod.id to your server
                const response = await fetch('/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        payment_method_id: paymentMethod.id,
                        amount: document.getElementById('amount').value,
                        currency: document.getElementById('currency').value,
                        _token: '<?php echo e(csrf_token()); ?>'
                    })
                });

                const result = await response.json();

                if (result.requires_action) {
                    // Use stripe.js to handle the next actions
                    const {
                        error,
                        paymentIntent
                    } = await stripe.handleCardAction(result.client_secret);

                    if (error) {
                        console.error(error);
                        cardErrors.textContent = error.message;
                        cardErrors.classList.remove('d-none');
                    } else {
                        // The card action has been handled
                        // The PaymentIntent can be confirmed again on the server
                        handleServerResponse(await confirmPaymentIntent(paymentIntent.id));
                    }
                } else {
                    handleServerResponse(result);
                }
            }
        });

        async function confirmPaymentIntent(paymentIntentId) {
            const response = await fetch('/handle-payment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    _token: '<?php echo e(csrf_token()); ?>',
                    payment_intent_id: paymentIntentId
                })
            });
            return response.json();
        }

        function handleServerResponse(response) {
            console.log(response);
            if (response.error) {
                cardErrors.textContent = response.error;
                cardErrors.classList.remove('d-none');
            } else if (response.requires_action) {
                // Use Stripe.js to handle required card action
                stripe.handleCardAction(
                    response.payment_intent_client_secret
                ).then(handleStripeJsResult);
            } else {
                // Show success message
                window.location.href = `/payment-success?payment-intent=${response.payment_intent_id}`;
            }
        }

        function handleStripeJsResult(result) {
            if (result.error) {
                cardErrors.textContent = result.error.message;
                cardErrors.classList.remove('d-none');
            } else {
                // The card action has been handled
                // The PaymentIntent can be confirmed again on the server
                confirmPaymentIntent(result.paymentIntent.id)
                    .then(handleServerResponse);
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\resources\views/payments/payment-intent.blade.php ENDPATH**/ ?>