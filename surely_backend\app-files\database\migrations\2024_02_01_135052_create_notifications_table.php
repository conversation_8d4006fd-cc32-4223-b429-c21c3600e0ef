<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('mobile_user_id');
            $table->boolean('contract_status_alert')->default(false);
            $table->boolean('surelypro_badges_alert')->default(false);
            $table->boolean('instant_book_reminder_alert')->default(false);
            $table->boolean('contract_start_date_reminder_alert')->default(false);
            $table->boolean('new_chat_message_alert')->default(false);
            $table->boolean('payments_alert')->default(false);
            $table->boolean('new_emergency_hire_job_alert')->default(false);
            $table->boolean('contract_status_mail')->default(false);
            $table->boolean('surelypro_badges_mail')->default(false);
            $table->boolean('instant_book_reminder_mail')->default(false);
            $table->boolean('contract_start_date_reminder_mail')->default(false);
            $table->boolean('new_chat_message_mail')->default(false);
            $table->boolean('payments_mail')->default(false);
            $table->boolean('new_emergency_hire_job_mail')->default(false);
            $table->timestamps();
            $table->foreign('mobile_user_id')->references('id')->on('mobile_users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}
