// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Text, View, Button, TextField, Tabs, Divider, useToast, Image, Breadcrumbs, Select } from 'reshaped';
import { Link, useNavigate } from 'react-router-dom';
import { paymentDetails, getPaymentDetails, addBankCardDetails, getBankCardDetails, addBankAccount, getBankAccount, addVatAndUtr, getGeneral } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';
import { headerLogo } from 'src/assets/images';
import { AppContext } from 'src/context/AppContext';
import stripeLogo from '../../../assets/icons/stripeLogo.svg';
import { getAllInvoices } from 'src/services/contracts';
import { parse, addMinutes, format } from 'date-fns';
import { Elements, useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import StripePaymentForm from '../../StripePaymentForm';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

const OperatorSettingsPaymentForm = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('0');
  const toast = useToast();
  const { fetchAppData } = useContext(AppContext);

  const [accountName, setAccountName] = useState<string>('');
  const [sortCode, setSortCode] = useState<string>('');
  const [accountNumber, setAccountNumber] = useState<string>('');
  const [utrNumber, setUtrNumber] = useState<string>('');
  const [vat, setVat] = useState<string>('0');

  const [cardHolderName, setCardHolderName] = useState('');

  const [invoices, setInvoices] = useState([]);

  const [isSaving, setIsSaving] = useState(false);

  const containsOnlyNumbers = (value: any) => /^\d+$/.test(value);
  const padWithZero = (value: any) => (value < 10 ? `0${value}` : value);

  const [hasGeneralDetails, setHasGeneralDetails] = useState(false);
  
  const checkGeneralDetails = async () => {
    try {
      const response = await getGeneral();
      const data = response?.data;
      
      const nameParts = data?.name?.split(' ') || [];
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(' ');
      
      const checks = {
        name: Boolean(data?.name?.trim()),
        email: Boolean(data?.email?.trim()),
        phone: Boolean(data?.phone?.trim()),
        address: Boolean(data?.address?.trim()),
        postal_code: Boolean(data?.postal_code?.trim()),
        address_1: Boolean(data?.address_1?.trim())
      };
      const hasRequired = Object.values(checks).every(value => value === true);
      setHasGeneralDetails(hasRequired);
    } catch (error) {
      setHasGeneralDetails(false);
    }
  };

  useEffect(() => {
    checkGeneralDetails();
  }, []);

  const handleSaveBankAccount = async () => {
    try {
      const bankAccountDetails = {
        name: accountName,
        routing_number: sortCode,
        account_number: accountNumber,
        operator_vat: vat,
      };
      const response = await addBankAccount(bankAccountDetails);
      
      if (response?.error) {
        toast.show({
          title: 'Error!',
          text: response?.message,
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      } else {
        toast.show({
          title: 'Done!',
          text: 'You have updated your bank account details!',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        
        // Add a small delay before reloading to ensure the toast is visible
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
      
      return response;
    } catch (error) {
      toast.show({
        title: 'Error!',
        text: 'Failed to save bank account details',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  };

  const handleSaveVatAndUtr = async () => {
    const vatAndUtr = {
      utr_number: utrNumber,
      operator_vat: vat,
    };
    const response = await addVatAndUtr(vatAndUtr);
    return response;
  };

  const handleTabChange = (args: { value: string; name?: string }) => {
    setActiveTab(args.value);
  };

  const submitFirstTab = () => {
    setIsSaving(true);
    const firstTabSettings: any = {
      accountName,
      sortCode,
      accountNumber,
      utrNumber,
    };
    paymentDetails(firstTabSettings)
      .then(() => fetchAppData())
      .finally(() => {
        setIsSaving(false);
      });
  };

  useEffect(() => {
    if (isSaving) {
      const timeoutId = setTimeout(() => {
        setIsSaving(false);
      }, 15000);

      return () => clearTimeout(timeoutId);
    }
  }, [isSaving]);

  useEffect(() => {
    setIsSaving(true);
    getBankAccount()
      .then((res) => {
        if (res?.error) {
          toast.show({
            title: 'Error',
            text: res.message || 'Failed to load bank account details',
            type: 'error'
          });
          return;
        }
        if (res?.data) {
          setAccountName(res.data.name || '');
          const sortCodeValue = res.data.sort_code || '';
          setSortCode(sortCodeValue ? `****${sortCodeValue.slice(-2)}` : '');
          setAccountNumber(res.data.last_digits ? `****${res.data.last_digits}` : '');
          setVat(res.data.operator_vat || '0');
          setUtrNumber(res.data.utr_number || '');
        }
      })
      .catch((error) => {
        console.error('Error fetching bank account:', error);
        toast.show({
          title: 'Error',
          text: 'Failed to load bank account details',
          type: 'error'
        });
      })
      .finally(() => {
        setIsSaving(false);
      });
  }, []);

  return (
    <View className='mx-auto max-w-[90%] overflow-hidden px-[12px] sm:w-auto sm:max-w-[100%] md:px-0'>
      {!hasGeneralDetails && (
        <View className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50'>
          <View className='w-[370px] rounded-[8px] bg-[#1C212BF7] p-6 text-white sm:w-[420px]'>
            <View className='flex items-start gap-4'>
              <Image src={surleyicon} alt='Surely Icon' className='h-[64px] w-[64px]' />
              <View className='w-[308px] text-left'>
                <Text className='rubik text-[14px] font-bold leading-[20px] text-[#EFF0F1]'>
                  Complete General Details First
                </Text>
                <Text className='rubik text-[14px] font-normal leading-[20px] text-[#EFF0F1]'>
                  Please complete your general details before adding bank account information.
                </Text>
                <Button
                  className='border-neutral bg-background-base mt-2.5 flex w-[173px] items-center justify-center rounded-[8px] border !bg-[#30374A] text-[14px] !text-white'
                  onClick={() => navigate('/operator-settings-general')}
                >
                  Go to General Settings
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}

      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/my-profile')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Profile</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => navigate('/operator-settings')}>
          <span className='rubik text-[16px] text-[#3C455D]'>Account settings</span>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <span className='rubik text-[16px] font-medium text-[#1A1A1A]'>Payment Details</span>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View className='mb-[16px] flex items-center p-0'>
        <Text className='text-foreground-neutral font-rufina-stencil text-[#323C58] lg:text-[32px] xl:leading-10'>Payment details</Text>
      </View>
      <Tabs value={activeTab} onChange={handleTabChange} variant='borderless'>
        <Tabs.List>
          {/* <Tabs.Item value='0'>
            <span className='rubik text-[14px] text-[#14171F]'>Bank details</span>
          </Tabs.Item> */}
          {/* if we need to add again the Bank details we should change the value in tabs item */}
          <Tabs.Item value='0'>
            <span className='rubik text-[14px] text-[#14171F]'>Bank details</span>
          </Tabs.Item>
          {/* <Tabs.Item value='2'>
            <span className='rubik text-[14px] text-[#14171F]'>Card details</span>
          </Tabs.Item> */}
          <Tabs.Item value='1'>
            <span className='rubik text-[14px] text-[#14171F]'>UTR</span>
          </Tabs.Item>
        </Tabs.List>
      </Tabs>
      {activeTab === '0' && (
        <View className='flex flex-col-reverse justify-between lg:flex-row'>
          <View className='mt-[16px] flex w-full flex-col sm:w-[536px]'>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A] '>Account holder name</Text>
              <TextField
                name='text'
                className=' mt-[5px] h-[48px] sm:w-[536px]'
                placeholder={accountName}
                value={accountName}
                onChange={(e) => setAccountName(e.value)}
              />
            </View>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik mt-4 font-medium leading-4 text-[#1A1A1A]'>Sort code</Text>
              <TextField
                name='text'
                className=' mt-[5px] h-[48px] sm:w-[536px]'
                placeholder={sortCode}
                value={sortCode}
                onChange={(e) => setSortCode(e.value)}
              />
            </View>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik mt-4 font-medium leading-4 text-[#1A1A1A]'>Account number</Text>
              <TextField
                name='text'
                className=' mt-[5px] h-[48px] sm:w-[536px]'
                placeholder={accountNumber}
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.value)}
              />
            </View>

            <Divider className='mt-[16px] h-[1px] w-full '></Divider>

            <View className='mt-[16px] flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleSaveBankAccount();
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex flex-col gap-[14px] sm:w-[313px] md:w-[530px] lg:ml-[135px] lg:mt-[0px] lg:w-[313px]'>
            <Image src={headerLogo} className='ml-[-5px] h-[41.274px] w-[109.76px] flex-shrink-0' />
            <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D] '>
              Receive payment immediately using our secure Stripe integration.
            </Text>
          </View>
        </View>
      )}
      {activeTab === '1' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-[16px] flex flex-col'>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>UTR number (optional)</Text>
              <TextField
                name='text'
                className=' mt-[5px] h-[48px] sm:w-[536px]'
                placeholder={utrNumber}
                value={utrNumber}
                onChange={(e) => setUtrNumber(e.value)}
              />
              {/* <a href='#' className='mt-4 text-base font-medium leading-6 text-[#323C58] underline'>
                Submit for validation
              </a> */}
            </View>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik mt-4 font-medium leading-4 text-[#1A1A1A]'>VAT (%)</Text>
              <Select
                name='vat'
                className=' mt-[5px] h-[48px] sm:w-[536px]'
                // placeholder={vat}
                value={vat}
                inputAttributes={{ type: 'number' }}
                onChange={(e) => setVat(e.value)}
                options={[
                  { label: '0%', value: '0' },
                  { label: '5%', value: '5' },
                  { label: '20%', value: '20' },
                ]}
                defaultValue={vat || '0'}
              />
            </View>
            <Divider className='mt-[16px] h-[1px] w-full'></Divider>

            <View className='mt-[16px] flex flex-row justify-between'>
              <Button
                variant='outline'
                icon={() => <span className='material-icons -mt-1'>clear</span>}
                onClick={() => navigate('/operator-settings')}
                className='border-neutral rubik flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 text-[16px] font-medium sm:w-[260px]'
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  handleSaveVatAndUtr().then((res) => {
                    if (res?.error) {
                      toast.show({
                        title: 'Error!',
                        text: res?.message,
                        actionsSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                      });
                    }
                    if (!res?.error) {
                      toast.show({
                        title: 'Done!',
                        text: 'You have updated your VAT and UTR!',
                        actionsSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                      });
                    }
                  });
                }}
                className={`border-neutral rubik flex items-center justify-center gap-2 rounded-[8px] border px-4 py-2 text-[16px] font-medium ${
                  isSaving ? '!border-[#0B80E7] !bg-[#fff] !text-[#0B80E7]' : '!bg-[#0B80E7] !text-[#ffff]'
                } h-[48px] sm:w-[260px]`}
              >
                {isSaving ? 'Saving...' : 'Save settings'}
              </Button>
            </View>
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {activeTab === '2' && (
        <View className='flex flex-col justify-between lg:flex-row'>
          <View className='mt-[16px] flex flex-col'>
            <View className='flex flex-col'>
              <Text className='text-neutral rubik font-medium leading-4 text-[#1A1A1A]'>Card holder name</Text>
              <TextField
                name='text'
                className='mt-[5px] h-[48px] sm:w-[536px]'
                placeholder='Card holder name'
                value={cardHolderName}
                onChange={(e) => setCardHolderName(e.value)}
              />
            </View>
            
            <View className='mt-4'>
              <StripePaymentForm cardHolderName={cardHolderName} />
            </View>
          </View>
        </View>
      )}
      {activeTab === '3' && (
        <View className='flex h-[583px] w-full flex-col justify-between sm:flex-row'>
          <View className='mt-5 flex h-full w-full flex-col gap-[16px] overflow-auto pr-2 sm:w-[536px]'>
            {invoices?.map((invoice) => {
              const contract = invoice?.contract;
              const contractId = contract?.id;
              const contractOrJobName = contract?.job?.id ? contract?.job?.post_name : 'Individual contract #' + contract?.id;
              const [datePart, timePart] = contract?.updated_at?.split('T');
              const [hours, minutes] = timePart.split(':');
              const parsedDate = parse(datePart, 'yyyy-MM-dd', new Date());
              const resultDate = addMinutes(parsedDate, parseInt(hours) * 60 + parseInt(minutes));
              const formattedDate = format(resultDate, 'iii d MMMM Y');

              return (
                <Link key={invoice.operative.id + '-' + invoice.contract.id} target='_blank' className='w-full ' to={`/invoice/${contractId}`}>
                  <View
                    key={contractId}
                    className='border-primary flex flex-col items-start justify-center gap-[12px] self-stretch rounded-[8px] border !bg-[#F4F5F7] p-[16px] '
                  >
                    <View className=''>
                      <Text className='rubik text-left text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>{contractOrJobName}</Text>
                      <Text className='rubik text-left text-[13px] font-normal leading-[20px] text-[#444B5F] '>{formattedDate}</Text>
                    </View>
                    <View className='border-primary flex h-[40px] w-full items-center justify-center self-stretch rounded-[4px] border bg-[#CDEDD5] px-[8px] py-[4px]'>
                      <Text className='rubik text-[13px] font-medium leading-[20px] !text-[#323C58]'>
                        <span className='!font-normal '>Total:</span>
                        &nbsp; £{contract?.total_amount}
                      </Text>
                    </View>
                  </View>
                </Link>
              );
            })}
          </View>
          <View className='ml-[0px] mt-[15px] flex w-[313px] flex-col sm:ml-[135px] sm:mt-[0px]'></View>
        </View>
      )}
      {/* <Divider className='w-full h-[1px] mt-[16px]'></Divider>
      <View className='flex flex-row justify-between mt-[16px]'>
        <Button
          variant='outline'
          icon={() => <span className='material-icons -mt-1'>clear</span>}
          onClick={() => navigate('/operator-settings')}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base rubik font-medium w-[120px] sm:w-[260px] h-[48px]'
        >
          Cancel
        </Button>
        <Button
          onClick={() => {
            handleSaveBankAccount();
            toast.show({
              title: 'Done!',
              text: 'You have updated your bank account details!',
              actionsSlot: (
                <Image src={surleyicon} className='w-[30px] h-[30px]' />
              ),
            });
          }}
          className='flex justify-center items-center px-4 py-2 gap-2 border border-neutral rounded bg-background-base rubik font-medium !text-white !bg-[#0B80E7] w-[120px] sm:w-[260px] h-[48px]'
        >
          Save settings
        </Button>
      </View> */}
    </View>
  );
};

// Separate wrapper component that provides Stripe context
const OperatorSettingsPayment = () => {
  return (
    <Elements stripe={stripePromise}>
      <OperatorSettingsPaymentForm />
    </Elements>
  );
};

export default OperatorSettingsPayment;
