<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateReviewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn('user_id');
            $table->dropColumn('booking_id');
            $table->json('rating')->change();
            $table->longText('comment')->change();
            $table->decimal('rating_avg')->after('rating')->nullable();
            $table->unsignedBigInteger('contract_id')->after('id')->nullable();
            $table->unsignedBigInteger('operative_id')->after('contract_id')->nullable();
            $table->unsignedBigInteger('client_id')->after('operative_id')->nullable();
            $table->boolean('share_with_surely')->after('client_id')->default(false);
            $table->boolean('share_with_client')->after('share_with_surely')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('reviews', function (Blueprint $table) {
            $table->dropColumn('contract_id');
            $table->dropColumn('share_with_surely');
            $table->dropColumn('share_with_client');
            $table->dropColumn('client_id');
            $table->dropColumn('operative_id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('booking_id')->nullable();
            $table->dropColumn('rating_avg');
        });
    }
}
