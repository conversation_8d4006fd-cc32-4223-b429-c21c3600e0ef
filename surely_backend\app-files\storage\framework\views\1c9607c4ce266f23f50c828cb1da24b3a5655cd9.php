
<?php $__env->startSection('title', 'Home'); ?>
<?php $__env->startSection('head'); ?>
<style>
    .surely-background {
        background-color: #323C58;
    }

    .h-200 {
        height: 100%;
    }
</style>
<?php $__env->stopSection(); ?>

<?php if(auth()->user()->role == \App\Models\User::Admin): ?>
<?php $__env->startSection('content'); ?>
<div class="container-fluid p-0">
    <div class="row col-lg-11 mr-auto ml-auto p-0">
        <div class="col-lg-7 widget h-100 m-2 ">
            <?php echo $contracts_chart->container(); ?>

        </div>
        <div class="col-lg-4 widget h-100 m-2">
            <?php echo $mobile_users_chart->container('chart-container'); ?>

        </div>
    </div>
    <div class="row col-lg-11 mr-auto ml-auto p-0">
        <div class="col-lg-4 widget h-100 m-2">
            <?php echo $job_postings_chart->container(); ?>

        </div>
        <div class="col-lg-4 widget h-100 m-2">
            <?php echo e($users_horizontal_bar_chart->container()); ?>

        </div>
        <div class="col-lg-3 col-12 m-2 d-flex flex-column justify-content-between">
            <div class="row justify-content-between">
                <div class="col-6 mb-1 pl-0 pr-1">
                    <div class="widget p-3 surely-background h-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-users">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>

                        <div class="widget-body mt-2">
                            <div class="">
                                <p class="h2 text-white"><?php echo e($clients); ?></p>
                            </div>
                            <div class="" style="">
                                <p class="h6 text-white" style="">Clients</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-6 mb-1 pl-1 pr-0">
                    <div class="widget p-3 surely-background h-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-users">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>

                        <div class="widget-body mt-2">
                            <div class="">
                                <p class="h2 text-white"><?php echo e($operatives); ?></p>
                            </div>
                            <div class="" style="">
                                <p class="h6 text-white" style="">Operatives</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-6 pl-0 pr-1">
                    <div class="widget p-3 surely-background h-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-briefcase">
                            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                        </svg>

                        <div class="widget-body mt-2">
                            <div class="">
                                <p class="h2 text-white"><?php echo e($completed_jobs); ?></p>
                            </div>
                            <div class="" style="">
                                <p class="h6 text-white" style="">Jobs Completed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-6 pl-1 pr-0">
                    <div class="widget p-3 surely-background h-100">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-briefcase">
                            <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                            <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                        </svg>

                        <div class="widget-body mt-2">
                            <div class="">
                                <p class="h2 text-white"><?php echo e($active_jobs); ?></p>
                            </div>
                            <div class="" style="">
                                <p class="h6 text-white" style="">Active Jobs</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php else: ?>
<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header"><?php echo e(__('Dashboard')); ?></div>
                <div class="card-body">
                    <?php if(session('status')): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo e(session('status')); ?>

                    </div>
                    <?php endif; ?>
                    <?php echo e(__('You are logged in!')); ?>

                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php endif; ?>
<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e($mobile_users_chart->cdn()); ?>"></script>
<?php echo e($mobile_users_chart->script()); ?>


<script src="<?php echo e($job_postings_chart->cdn()); ?>"></script>
<?php echo e($job_postings_chart->script()); ?>


<script src="<?php echo e($contracts_chart->cdn()); ?>"></script>
<?php echo e($contracts_chart->script()); ?>


<script src="<?php echo e($users_horizontal_bar_chart->cdn()); ?>"></script>
<?php echo e($users_horizontal_bar_chart->script()); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\resources\views/home.blade.php ENDPATH**/ ?>